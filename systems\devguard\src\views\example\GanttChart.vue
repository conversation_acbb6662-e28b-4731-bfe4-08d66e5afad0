<template>
  <div class="w-full flex flex-col items-center gap-[20px] py-[10px]">
    <GanttChart
      class="w-full rd-[12px] bg-FO-Container-Fill3"
      :items="testGanttItems"
      :min="displayRange[0]"
      :max="displayRange[1]"
      :axisLabelFormat="(percentage) => {
        return dayjs(percentage).format('HH:mm');
      }"
      :rowHeight="15"
      :rowGap="10"
      :tickInterval="tickInterval"
      :precision="60 * 1000 * 10"
      :renderItem="renderItem"
      itemClass="hover:scale-101"
      axisDividerClass="bg-FO-Container-Fill4"
    />
  </div>
</template>

<script lang="tsx" setup>
import { type GanttItem, FollowTooltip, GanttChart } from '@hg-tech/oasis-common';
import { useLatestPromise } from '@hg-tech/utils-vue';
import dayjs from 'dayjs';
import { getTaskItemsApi } from '../../api';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';

import { type PropType, computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { checkStateType } from './type.data';

const props = defineProps({
  instanceID: {
    type: Number as PropType<number>,
    required: true,
  },
  displayRange: {
    type: Array as unknown as PropType<[number, number]>,
    default: () => [0, 0],
  },
});
// const testGanttItems = ref<GanttItem[]>([
//   {
//     id: '1',
//     label: 'Task 1',
//     start: 1719730400000, // 2024-07-01 00:00:00 GMT+8
//     end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
//   },
//   {
//     id: '2',
//     label: 'Task 2',
//     start: 1719841200000, // 2024-07-01 03:00:00 GMT+8
//     end: 1719862800000, // 2024-07-01 09:00:00 GMT+8
//   },
//   {
//     id: '3',
//     label: 'Task 3',
//     start: 1719873600000, // 2024-07-01 12:00:00 GMT+8
//     end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
//   },
//   {
//     id: '4',
//     label: 'Task 4',
//     start: 1719873600000, // 2024-07-01 00:00:00 GMT+8
//     end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
//   },
//   {
//     id: '5',
//     label: 'Task 5',
//     start: 1719830400000, // 2024-07-01 00:00:00 GMT+8
//     end: 1719862800000, // 2024-07-02 00:00:00 GMT+8
//   },
// ]);
const emits = defineEmits<{
  (e: 'itemClick', value: GanttItem): void;
}>();
const router = useRouter();
const routeParams = router.currentRoute.value.params;
const forgeonConfig = useForgeonConfigStore(store);
const { execute: getTaskItemsExecute, data: taskItems } = useLatestPromise(getTaskItemsApi);
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const testGanttItems = ref<GanttItem[]>([]);
watch(() => [props.displayRange, props.instanceID], async () => {
  await getTaskItemsExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID!, instanceID: props.instanceID, startTime: props.displayRange[0], endTime: props.displayRange[1] }, {});
  testGanttItems.value = [];
  const a = [
    {
      instanceID: 1,
      title: '检查内容', // 检查项名称
      distinguishCode: 1, // 区分码，在时间段重叠时用于固定上下顺序
      startTime: 1756004275106, // 开始时间戳
      endTime: 1756090675107,
      status: 1, // 1 - 检查中；2 - 通过；3 - 出错；4 - 中断
      username: 'wanghongyi',
      user: {
        ID: 773,
        CreatedAt: '2025-04-27T11:20:05.359+08:00',
        UpdatedAt: '2025-04-27T11:26:24.565+08:00',
        uuid: 'cd7aa65c-ca3c-4a63-b17c-12dd584372ee',
        userName: 'wanghongyi',
        nickName: '王弘毅(mikyi)',
        sideMode: 'dark',
        headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
        baseColor: '#fff',
        activeColor: '#1890ff',
        authorityId: 888,
        authority: {
          CreatedAt: '0001-01-01T00:00:00Z',
          UpdatedAt: '0001-01-01T00:00:00Z',
          DeletedAt: null,
          authorityId: 0,
          authorityName: '',
          parentId: 0,
          dataAuthorityId: null,
          children: null,
          menus: null,
          defaultRouter: '',
        },
        authorities: null,
        phone: '',
        email: '<EMAIL>',
        enable: 1,
        authType: 1,
        openID: '',
      },
      clRecordID: 1,
    },
  ];
  a.forEach((item: any) => {
    testGanttItems.value.push({
      id: item.instanceID,
      label: item.title,
      start: item.startTime,
      end: item.endTime,
      meta: {
        title: item.title,
        user: item.user.nickName,
        status: item.status,

      },
    });
  });
}, {
  immediate: true,
  deep: true,
});
function itemClick(item: GanttItem) {
  emits('itemClick', item);
}
const tickInterval = computed(() => {
  const range = props.displayRange[1] - props.displayRange[0];
  // 以小时为单位，间隔为1小时或2小时（以毫秒为单位）
  const oneHour = 3600 * 1000;
  return range < 24 * oneHour ? oneHour : 2 * oneHour;
});

function renderItem(item: GanttItem) {
  return (
    <FollowTooltip offset={-50} tooltipStyle={{ minWidth: '120px' }}>
      {{
        default: () => (
          <div class="h-full w-full flex items-center justify-center" className={item.meta.status === checkStateType.Checking ? 'c-FO-Functional-Warning1-Default' : (status === checkStateType.Passed ? 'c-FO-Functional-Success1-Default' : 'c-FO-Functional-Error1-Default')} onClick={() => itemClick(item)}>
            <span class="text-FO-Text-Primary text-[12px]" />
          </div>
        ),
        content: () => (
          <div class="flex flex-col items-center justify-center">
            <div class="text-FO-Text-Primary text-[12px]"> {dayjs(item.start).format('HH:mm:ss')} - {dayjs(item.end).format('HH:mm:ss')}</div>
            <div class="text-FO-Text-Secondary text-[12px]">{item.meta.title}</div>
            <div class="text-FO-Text-Secondary text-[12px]">来自 {item.meta.user}</div>
            <div class="text-FO-Text-Secondary text-[12px]">点击查看详情</div>
          </div>
        ),
      }}
    </FollowTooltip>
  );
}
</script>
